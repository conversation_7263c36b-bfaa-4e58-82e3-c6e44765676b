import SwiftUI
import MarkdownUI

struct HelpScene: View {
  @AppStorage(UserDefaultsKeys.selectedLocale.rawValue) var selectedLocale: LocaleConstants = .system

  // MARK: - life cycle

  var body: some View {
    VStack(alignment: .center, spacing: 0) {
      navi
      list
    }.background(Color.Neumorphic.main)
  }

  var navi: some View {
    ZStack(alignment: .center) {
      Text("Help")
        .font(.title)
        .foregroundColor(Color.Neumorphic.secondary)
      HStack {
        BackButton()
          .padding(.horizontal, Constant.padding * 2)
        Spacer()
      }
    }
    .padding(.vertical, Constant.padding)
    .foregroundColor(Color.Neumorphic.secondary)
    .background(Color.Neumorphic.main)
  }

  var list: some View {
    ZStack(alignment: .top) {
      Color.Neumorphic.main
        .ignoresSafeArea()
      VStack(alignment: .center, spacing: 10.0) {
        Link("Online help", destination: URL(string: selectedLocale.helpUrl)!)
          .padding(4)
          .overlay(
            RoundedRectangle(cornerRadius: 4)
              .stroke(Color.accent, lineWidth: 1)
          )

        ScrollView {
          Markdown(helpMarkdownContent)
            .foregroundColor(Color.Neumorphic.secondary)
            .accentColor(.accentColor)
        }
      }
      .padding(Constant.padding)
      .ignoresSafeArea(edges: .bottom)
    }
  }

  // MARK: - Computed Properties

  private var helpMarkdownContent: String {
    switch selectedLocale {
    case .zh_Hant:
      return HelpContent.zhHant(includeMacOSContent: true)
    case .zh_Hans:
      return HelpContent.zhHans(includeMacOSContent: true)
    default:
      return HelpContent.en(includeMacOSContent: true)
    }
  }
}

// MARK: - Help Content

struct HelpContent {
  static func en(includeMacOSContent: Bool = true) -> String {
    let baseContent = """
# OpenCCman Help

## Introduction

**Open Chinese Convert (OpenCC)** is an opensource project for conversion between Traditional Chinese, Simplified Chinese and Japanese Kanji (Shinjitai). It supports character-level conversion, phrase-level conversion, variant conversion and regional idioms among Mainland China, Taiwan and Hong Kong. This is not a translation tool between Mandarin and Cantonese, etc.

Convert Chinese text with [OpenCC](https://github.com/BYVoid/OpenCC)

## Features

Strictly distinguish "One Simplified to Many Traditional" and "One Simplified to Many Variants".

Completely compatible with different variants, can achieve dynamic replacement.

Strictly review One Simplified to Many Traditional entries, the principle is "divide if possible".

Support conversion between different variants and regional idioms in Mainland China, Taiwan, Hong Kong, such as "裏" "裡", "鼠標" "滑鼠".
"""

    let macOSContent = """

## Global Service (macOS)

OpenCCman provides multiple ways to convert Chinese text in any application:

### Method 1: Global Keyboard Shortcut (Recommended)

1. Go to **Settings > Global Shortcut** to configure your shortcut
2. Select Chinese text in any application
3. Press your configured shortcut (default: `⌘⌥R`)
4. Text will be automatically converted and replaced
5. OpenCCman will open and display the original and converted text

### Method 2: Right-click Services

OpenCCman provides two right-click services:

• **"Convert Chinese Text with OpenCCman"** - Automatically converts and replaces the selected text
• **"Open Selected Text in OpenCCman"** - Opens the app with the selected text for manual conversion

**Steps:**
1. Select text in any app (Safari, TextEdit, etc.)
2. Right-click and choose your preferred service from the Services menu
3. For convert service: text is automatically converted and replaced
4. For open service: OpenCCman opens with the text ready for conversion

> **Tip:** You can also assign keyboard shortcuts to these services in **System Settings > Keyboard > Keyboard Shortcuts > Services > Text** for faster access.

### Service Details

• **Convert Service:** Suitable for quick automatic conversion when you're confident in current settings
• **Open Service:** Suitable when you want to see the text, adjust conversion settings, or keep both original and converted text visible
• **Both services** work in any macOS app that supports text selection

## Conclusion

**Keyboard shortcut method** is fastest for immediate conversion. Use **"Open Selected Text" service** when you want to review or adjust settings before conversion.
"""

    let footer = """

---

*For more information, visit [Online Help](https://gewill.org/2023/12/17/introducing-OpenCCman-en/)*
"""

    #if os(macOS)
    return baseContent + (includeMacOSContent ? macOSContent : "") + footer
    #else
    return baseContent + footer
    #endif
  }

  static func zhHans(includeMacOSContent: Bool = true) -> String {
    let baseContent = """
# OpenCCman 帮助

## 介绍

**开放中文转换 (OpenCC, 开放中文转换)** 是一个开源项目，用于繁体中文、简体中文和日文汉字（新字体）之间的转换。它支持字符级和短语级转换、字符变体转换以及中国大陆、台湾和香港之间的地区习语转换。这不是普通话和粤语等之间的翻译工具。

使用 [OpenCC](https://github.com/BYVoid/OpenCC) 转换中文文字

## 功能特性

严格区分「一简对多繁」和「一简对多异」。

完全兼容不同变体，可以实现动态替换。

严格审查一简对多繁条目，原则是「能分则分」。

支持中国大陆、台湾、香港不同变体和地区习惯用词转换，如「裏」「裡」、「鼠标」「滑鼠」。
"""

    let macOSContent = """

## 全局服务 (macOS)

OpenCCman 提供多种方式在任何应用程序中转换中文文字：

### 方法一：全局键盘快捷键（推荐）

1. 前往 **设置 > 全局快捷键** 配置您的快捷键
2. 在任何应用程序中选择中文文字
3. 按下您配置的快捷键（默认：`⌘⌥R`）
4. 文字将自动转换并替换
5. OpenCCman 将打开并显示原文和转换后的文字

### 方法二：右键服务

OpenCCman 提供两种右键服务：

• **「使用 OpenCCman 转换中文文本」** - 自动转换并替换选中的文字
• **「在 OpenCCman 中打开选中文字」** - 打开应用程序，将选中文字载入以便手动转换

**步骤：**
1. 在任何应用程序（Safari、文本编辑等）中选择文字
2. 右键点击并从「服务」菜单中选择您偏好的服务
3. 转换服务：文字将自动转换并替换
4. 打开服务：OpenCCman 将打开并载入文字，准备进行转换

> **提示：** 您也可以在 **系统设置 > 键盘 > 键盘快捷键 > 服务 > 文本** 中为这些服务分配键盘快捷键，以便更快速地访问。

### 服务详情

• **转换服务：** 适合快速自动转换，当您对当前设置有信心时使用
• **打开服务：** 适合想要查看文字、调整转换设置或保持原文和转换文字同时可见时使用
• **两种服务** 都可在任何支持文字选择的 macOS 应用程序中使用

## 结论

**键盘快捷键方式** 最快速，适合立即转换。当您想要在转换前查看或调整设置时，请使用 **「打开选中文字」服务**。
"""

    let footer = """

---

*更多信息请访问 [在线帮助](https://gewill.org/2023/12/17/introducing-OpenCCman-zh-Hans/)*
"""

    #if os(macOS)
    return baseContent + (includeMacOSContent ? macOSContent : "") + footer
    #else
    return baseContent + footer
    #endif
  }

  static func zhHant(includeMacOSContent: Bool = true) -> String {
    let baseContent = """
# OpenCCman 說明

## 介紹

**開放中文轉換 (OpenCC, 開放中文轉換)** 是一個開源專案，用於繁體中文、簡體中文和日文漢字（新字體）之間的轉換。它支援字元級和短語級轉換、字元變體轉換以及中國大陸、台灣和香港之間的地區習語轉換。這不是普通話和粵語等之間的翻譯工具。

使用 [OpenCC](https://github.com/BYVoid/OpenCC) 轉換中文文字

## 功能特性

嚴格區分「一簡對多繁」和「一簡對多異」。

完全相容不同變體，可以實現動態替換。

嚴格審查一簡對多繁條目，原則是「能分則分」。

支援中國大陸、台灣、香港不同變體和地區習慣用詞轉換，如「裏」「裡」、「鼠標」「滑鼠」。
"""

    let macOSContent = """

## 全域服務 (macOS)

OpenCCman 提供多種方式在任何應用程式中轉換中文文字：

### 方法一：全域鍵盤快速鍵（推薦）

1. 前往 **設定 > 全域快速鍵** 配置您的快速鍵
2. 在任何應用程式中選取中文文字
3. 按下您配置的快速鍵（預設：`⌘⌥R`）
4. 文字將自動轉換並替換
5. OpenCCman 將開啟並顯示原文和轉換後的文字

### 方法二：右鍵服務

OpenCCman 提供兩種右鍵服務：

• **「使用 OpenCCman 轉換中文文本」** - 自動轉換並替換選取的文字
• **「在 OpenCCman 中開啟選取文字」** - 開啟應用程式，將選取文字載入以便手動轉換

**步驟：**
1. 在任何應用程式（Safari、文字編輯等）中選取文字
2. 右鍵點擊並從「服務」選單中選擇您偏好的服務
3. 轉換服務：文字將自動轉換並替換
4. 開啟服務：OpenCCman 將開啟並載入文字，準備進行轉換

> **提示：** 您也可以在 **系統設定 > 鍵盤 > 鍵盤快速鍵 > 服務 > 文字** 中為這些服務分配鍵盤快捷鍵，以便更快速地訪問。

### 服務詳情

• **轉換服務：** 適合快速自動轉換，當您對目前設定有信心時使用
• **開啟服務：** 適合想要查看文字、調整轉換設定或保持原文和轉換文字同時可見時使用
• **兩種服務** 都可在任何支援文字選取的 macOS 應用程式中使用

## 結論

**鍵盤快速鍵方式** 最快速，適合立即轉換。當您想要在轉換前查看或調整設定時，請使用 **「開啟選取文字」服務**。
"""

    let footer = """

---

*更多資訊請造訪 [線上說明](https://gewill.org/2023/12/17/introducing-OpenCCman-zh-Hant/)*
"""

    #if os(macOS)
    return baseContent + (includeMacOSContent ? macOSContent : "") + footer
    #else
    return baseContent + footer
    #endif
  }
}

struct HelpView_Previews: PreviewProvider {
  static var previews: some View {
    HelpScene()
  }
}
