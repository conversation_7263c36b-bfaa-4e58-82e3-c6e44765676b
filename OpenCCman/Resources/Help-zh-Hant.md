# OpenCCman 說明

## 介紹

**開放中文轉換 (OpenCC, 開放中文轉換)** 是一個開源專案，用於繁體中文、簡體中文和日文漢字（新字體）之間的轉換。它支援字元級和短語級轉換、字元變體轉換以及中國大陸、台灣和香港之間的地區習語轉換。這不是普通話和粵語等之間的翻譯工具。

使用 [OpenCC](https://github.com/BYVoid/OpenCC) 轉換中文文字

## 功能特性

嚴格區分「一簡對多繁」和「一簡對多異」。

完全相容不同變體，可以實現動態替換。

嚴格審查一簡對多繁條目，原則是「能分則分」。

支援中國大陸、台灣、香港不同變體和地區習慣用詞轉換，如「裏」「裡」、「鼠標」「滑鼠」。

## 全域服務 (macOS)

OpenCCman 提供多種方式在任何應用程式中轉換中文文字：

### 方法一：全域鍵盤快速鍵（推薦）

1. 前往 **設定 > 全域快速鍵** 配置您的快速鍵
2. 在任何應用程式中選取中文文字
3. 按下您配置的快速鍵（預設：`⌘⌥R`）
4. 文字將自動轉換並替換
5. OpenCCman 將開啟並顯示原文和轉換後的文字

### 方法二：右鍵服務

OpenCCman 提供兩種右鍵服務：

• **「使用 OpenCCman 轉換中文文本」** - 自動轉換並替換選取的文字
• **「在 OpenCCman 中開啟選取文字」** - 開啟應用程式，將選取文字載入以便手動轉換

**步驟：**
1. 在任何應用程式（Safari、文字編輯等）中選取文字
2. 右鍵點擊並從「服務」選單中選擇您偏好的服務
3. 轉換服務：文字將自動轉換並替換
4. 開啟服務：OpenCCman 將開啟並載入文字，準備進行轉換

> **提示：** 您也可以在 **系統設定 > 鍵盤 > 鍵盤快速鍵 > 服務 > 文字** 中為這些服務分配鍵盤快捷鍵，以便更快速地訪問。

### 服務詳情

• **轉換服務：** 適合快速自動轉換，當您對目前設定有信心時使用
• **開啟服務：** 適合想要查看文字、調整轉換設定或保持原文和轉換文字同時可見時使用
• **兩種服務** 都可在任何支援文字選取的 macOS 應用程式中使用

## 結論

**鍵盤快速鍵方式** 最快速，適合立即轉換。當您想要在轉換前查看或調整設定時，請使用 **「開啟選取文字」服務**。

---

*更多資訊請造訪 [線上說明](https://gewill.org/2023/12/17/introducing-OpenCCman-zh-Hant/)*
