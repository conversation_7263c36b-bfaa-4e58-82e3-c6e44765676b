import Foundation
import SwiftUI

extension Image {
  // MARK: - General

  static let chevronRight = Image(systemName: "chevron.right")
  static let heart = Image(systemName: "heart")
  static let user = Image(systemName: "person")
  static let questionmark = Image(systemName: "questionmark.circle")
  static let reset = Image(systemName: "arrow.triangle.2.circlepath")
  static let xmark = Image(systemName: "xmark")

  // MARK: - Settings

  static let settings = Image(systemName: "gear")
  static let crown = Image(systemName: "crown")
}
