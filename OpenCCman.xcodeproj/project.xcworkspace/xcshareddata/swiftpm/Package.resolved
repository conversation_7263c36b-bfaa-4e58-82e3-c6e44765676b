{"originHash": "efdd05504672650817bf0cf28a2d3a5ec792aba2dd59c4d15af51cce1eed169d", "pins": [{"identity": "attributedtext", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/AttributedText", "state": {"revision": "0e811cb14de367bbd58a94d12259687e5ee08bea", "version": "1.0.1"}}, {"identity": "bettersafariview", "kind": "remoteSourceControl", "location": "https://github.com/stleamist/BetterSafariView", "state": {"revision": "884533749e55949b2566030948c6fdbe2873ed0c", "version": "2.4.2"}}, {"identity": "colorfulx", "kind": "remoteSourceControl", "location": "https://github.com/Lakr233/ColorfulX", "state": {"revision": "4b48da803e2e02bdd29c031802d995bf28eea486", "version": "5.7.0"}}, {"identity": "colorvector", "kind": "remoteSourceControl", "location": "https://github.com/Lakr233/ColorVector.git", "state": {"revision": "6da8726bf38d68eb943d0f2139ac2a1fac70e65b", "version": "1.0.4"}}, {"identity": "combine-schedulers", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/combine-schedulers", "state": {"revision": "ec62f32d21584214a4b27c8cee2b2ad70ab2c38a", "version": "0.11.0"}}, {"identity": "iqkeyboardmanager", "kind": "remoteSourceControl", "location": "https://github.com/hackiftekhar/IQKeyboardManager", "state": {"revision": "c00b1ae9fa1ad8af4465bb6ca901f6943fc98eba", "version": "6.5.16"}}, {"identity": "keyboardshortcuts", "kind": "remoteSourceControl", "location": "https://github.com/sindresorhus/KeyboardShortcuts", "state": {"revision": "045cf174010beb335fa1d2567d18c057b8787165", "version": "2.3.0"}}, {"identity": "msdisplaylink", "kind": "remoteSourceControl", "location": "https://github.com/Lakr233/MSDisplayLink.git", "state": {"revision": "ebf5823cb5fc1326639d9a05bc06d16bbe82989f", "version": "2.0.8"}}, {"identity": "networkimage", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/NetworkImage", "state": {"revision": "c97f83e771c95186af80ef6118f158852a9e54be", "version": "4.0.1"}}, {"identity": "neumorphic", "kind": "remoteSourceControl", "location": "https://github.com/gewill/neumorphic.git", "state": {"branch": "master", "revision": "3e54160a6a72596fb95b110fc160de5116429ec2"}}, {"identity": "purchases-ios-spm", "kind": "remoteSourceControl", "location": "https://github.com/RevenueCat/purchases-ios-spm.git", "state": {"revision": "709554156bb93c909b362e0b30ec0f2afee3df6e", "version": "5.33.1"}}, {"identity": "springinterpolation", "kind": "remoteSourceControl", "location": "https://github.com/Lakr233/SpringInterpolation.git", "state": {"revision": "f9ae95ece5d6b7cdceafd4381f1d5f0f9494e5d2", "version": "1.3.1"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "a46265bb4f75808b0e15d971eebc408f557870a3", "version": "0.1.2"}}, {"identity": "swift-markdown-ui", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/swift-markdown-ui", "state": {"revision": "6a33c60ad430772d69ac5278e35474d6b3e5d30a", "version": "1.1.1"}}, {"identity": "swiftcommonmark", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/SwiftCommonMark", "state": {"revision": "ed252beaddecce28ea6363f800c773d6169011b8", "version": "1.0.0"}}, {"identity": "swiftui-introspect", "kind": "remoteSourceControl", "location": "https://github.com/siteline/swiftui-introspect", "state": {"revision": "807f73ce09a9b9723f12385e592b4e0aaebd3336", "version": "1.3.0"}}, {"identity": "swiftuioverlaycontainer", "kind": "remoteSourceControl", "location": "https://github.com/fatbobman/SwiftUIOverlayContainer", "state": {"revision": "64a124ba613adc9f2bb7bf00f5a5e8b22a816d14", "version": "2.4.1"}}, {"identity": "swiftuirouter", "kind": "remoteSourceControl", "location": "https://github.com/frzi/SwiftUIRouter.git", "state": {"revision": "e0cb32b54bb429aa43ce30240b0036bf223a62a3", "version": "1.4.0"}}, {"identity": "swiftyopencc", "kind": "remoteSourceControl", "location": "https://github.com/gewill/SwiftyOpenCC", "state": {"branch": "master", "revision": "c11cb6431ad17187111d80b0f3daf6485f8a7e6c"}}, {"identity": "swiftyuserdefaults", "kind": "remoteSourceControl", "location": "https://github.com/sunshinejr/SwiftyUserDefaults", "state": {"revision": "f66bcd04088582c8fbb5cb8554d577e303bae396", "version": "5.3.0"}}, {"identity": "visualeffects", "kind": "remoteSourceControl", "location": "https://github.com/twostraws/VisualEffects", "state": {"revision": "8ff7f90f9650d9db5774275d0b1f07ef642ecf4d", "version": "1.0.3"}}, {"identity": "xctest-dynamic-overlay", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/xctest-dynamic-overlay", "state": {"revision": "50843cbb8551db836adec2290bb4bc6bac5c1865", "version": "0.9.0"}}], "version": 3}